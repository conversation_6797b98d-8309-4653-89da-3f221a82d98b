using System.Net;
using System.Text;
using MailKit.Net.Smtp;
using MimeKit;

namespace FTPHealthChecker;

class Program
{
    // FTP Configuration - Replace with actual values
    private const string FtpServerUrl = "ftp://yourserver.com/";
    private const string FtpUsername = "ftpusername";
    private const string FtpPassword = "ftppassword";

    // Email Configuration - Replace with actual values
    private const string SmtpServer = "smtp.gmail.com";
    private const int SmtpPort = 587;
    private const string EmailUsername = "<EMAIL>";
    private const string EmailPassword = "your-app-password";
    private const string FromEmail = "<EMAIL>";
    private const string FromName = "FTP Health Checker";

    // Email Recipients
    private const string ToEmail = "<EMAIL>";
    private static readonly string[] CcEmails = {
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    };

    static async Task<int> Main(string[] args)
    {
        try
        {
            Console.WriteLine("Starting FTP Health Check...");

            // Create a copy of the dummy XML file for this health check
            string sourceFile = "dummy_check.xml";
            string tempFile = $"healthcheck_{DateTime.Now:yyyyMMdd_HHmmss}.xml";

            if (!File.Exists(sourceFile))
            {
                throw new FileNotFoundException($"Source file '{sourceFile}' not found. Make sure it exists in the application directory.");
            }

            // Copy the dummy file
            File.Copy(sourceFile, tempFile);
            Console.WriteLine($"Created temporary file: {tempFile}");

            // Attempt FTP upload
            bool uploadSuccess = await UploadFileToFtp(tempFile);

            if (uploadSuccess)
            {
                // Upload successful - delete the temporary file and exit silently
                File.Delete(tempFile);
                Console.WriteLine("FTP Health Check completed successfully.");
                return 0; // Success exit code
            }
            else
            {
                // This shouldn't happen as exceptions should be caught, but just in case
                File.Delete(tempFile);
                await SendFailureEmail("Unknown error occurred during FTP upload.");
                return 1; // Failure exit code
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"FTP Health Check failed: {ex.Message}");

            // Clean up any temporary files
            CleanupTempFiles();

            // Send failure email
            await SendFailureEmail(ex.Message);
            return 1; // Failure exit code
        }
    }

    private static async Task<bool> UploadFileToFtp(string localFilePath)
    {
        try
        {
            Console.WriteLine($"Uploading {localFilePath} to FTP server...");

            // Create FTP request
            string ftpUrl = FtpServerUrl.TrimEnd('/') + "/" + Path.GetFileName(localFilePath);
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
            request.Method = WebRequestMethods.Ftp.UploadFile;
            request.Credentials = new NetworkCredential(FtpUsername, FtpPassword);
            request.UseBinary = true;
            request.UsePassive = true;
            request.KeepAlive = false;

            // Read file content
            byte[] fileContents = await File.ReadAllBytesAsync(localFilePath);
            request.ContentLength = fileContents.Length;

            // Upload file
            using (Stream requestStream = await request.GetRequestStreamAsync())
            {
                await requestStream.WriteAsync(fileContents, 0, fileContents.Length);
            }

            // Get response
            using (FtpWebResponse response = (FtpWebResponse)await request.GetResponseAsync())
            {
                Console.WriteLine($"Upload completed. Status: {response.StatusDescription}");
                return response.StatusCode == FtpStatusCode.ClosingData ||
                       response.StatusCode == FtpStatusCode.FileActionOK;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"FTP upload failed: {ex.Message}");
            throw; // Re-throw to be handled by main method
        }
    }

    private static async Task SendFailureEmail(string errorMessage)
    {
        try
        {
            Console.WriteLine("Sending failure notification email...");

            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(FromName, FromEmail));
            message.To.Add(new MailboxAddress("", ToEmail));

            // Add CC recipients
            foreach (string ccEmail in CcEmails)
            {
                message.Cc.Add(new MailboxAddress("", ccEmail));
            }

            message.Subject = "FTP Health Check Failed";

            var bodyBuilder = new BodyBuilder();
            bodyBuilder.TextBody = $@"FTP Health Check Failed

The scheduled FTP health check has failed with the following error:

Error Message: {errorMessage}

Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

Please check the FTP server configuration and connectivity.

This is an automated message from the FTP Health Checker application.";

            message.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                await client.ConnectAsync(SmtpServer, SmtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(EmailUsername, EmailPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
            }

            Console.WriteLine("Failure notification email sent successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to send email notification: {ex.Message}");
            // Don't throw here as we don't want email failures to mask the original FTP failure
        }
    }

    private static void CleanupTempFiles()
    {
        try
        {
            // Clean up any temporary health check files that might be left behind
            string[] tempFiles = Directory.GetFiles(".", "healthcheck_*.xml");
            foreach (string file in tempFiles)
            {
                try
                {
                    File.Delete(file);
                    Console.WriteLine($"Cleaned up temporary file: {file}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Could not delete temporary file {file}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during cleanup: {ex.Message}");
        }
    }
}
