using System.Text;

namespace FTPHealthChecker;

public class FileLogger
{
    private readonly string _logFilePath;
    private readonly int _maxLogFileSizeMB;
    private readonly int _maxLogFiles;
    private readonly object _lockObject = new();

    public FileLogger(LoggingSettings settings)
    {
        _logFilePath = settings.LogFilePath;
        _maxLogFileSizeMB = settings.MaxLogFileSizeMB;
        _maxLogFiles = settings.MaxLogFiles;

        // Ensure log directory exists
        var logDirectory = Path.GetDirectoryName(_logFilePath);
        if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }
    }

    public void LogInfo(string message)
    {
        WriteLog("INFO", message);
    }

    public void LogError(string message)
    {
        WriteLog("ERROR", message);
    }

    public void LogError(string message, Exception ex)
    {
        WriteLog("ERROR", $"{message}: {ex.Message}\nStackTrace: {ex.StackTrace}");
    }

    public void LogWarning(string message)
    {
        WriteLog("WARNING", message);
    }

    private void WriteLog(string level, string message)
    {
        lock (_lockObject)
        {
            try
            {
                // Check if log rotation is needed
                RotateLogIfNeeded();

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}{Environment.NewLine}";
                File.AppendAllText(_logFilePath, logEntry, Encoding.UTF8);

                // Also write to console
                Console.WriteLine($"[{level}] {message}");
            }
            catch (Exception ex)
            {
                // If logging fails, at least write to console
                Console.WriteLine($"[{level}] {message}");
                Console.WriteLine($"[ERROR] Failed to write to log file: {ex.Message}");
            }
        }
    }

    private void RotateLogIfNeeded()
    {
        if (!File.Exists(_logFilePath))
            return;

        var fileInfo = new FileInfo(_logFilePath);
        var maxSizeBytes = _maxLogFileSizeMB * 1024 * 1024;

        if (fileInfo.Length > maxSizeBytes)
        {
            // Rotate logs
            var directory = Path.GetDirectoryName(_logFilePath) ?? "";
            var fileName = Path.GetFileNameWithoutExtension(_logFilePath);
            var extension = Path.GetExtension(_logFilePath);

            // Move existing numbered logs
            for (int i = _maxLogFiles - 1; i >= 1; i--)
            {
                var oldFile = Path.Combine(directory, $"{fileName}.{i}{extension}");
                var newFile = Path.Combine(directory, $"{fileName}.{i + 1}{extension}");

                if (File.Exists(oldFile))
                {
                    if (File.Exists(newFile))
                        File.Delete(newFile);
                    File.Move(oldFile, newFile);
                }
            }

            // Move current log to .1
            var firstRotatedFile = Path.Combine(directory, $"{fileName}.1{extension}");
            if (File.Exists(firstRotatedFile))
                File.Delete(firstRotatedFile);
            File.Move(_logFilePath, firstRotatedFile);
        }
    }
}
