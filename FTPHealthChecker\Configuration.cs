namespace FTPHealthChecker;

public class FtpSettings
{
    public string ServerUrl { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public class EmailSettings
{
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string ToEmail { get; set; } = string.Empty;
    public string[] CcEmails { get; set; } = Array.Empty<string>();
}

public class LoggingSettings
{
    public string LogFilePath { get; set; } = "logs/ftp-health-check.log";
    public int MaxLogFileSizeMB { get; set; } = 10;
    public int MaxLogFiles { get; set; } = 5;
}

public class AppConfiguration
{
    public FtpSettings FtpSettings { get; set; } = new();
    public EmailSettings EmailSettings { get; set; } = new();
    public LoggingSettings LoggingSettings { get; set; } = new();
}
