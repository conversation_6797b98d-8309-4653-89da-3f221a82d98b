# FTP Health Checker

A .NET 8 console application that performs FTP server health checks by uploading a dummy XML file and sends email notifications on failure.

## Features

- Creates a temporary copy of a dummy XML file for each health check
- Uploads the file to a remote FTP server
- Deletes the temporary file on successful upload
- Sends email notifications with error details if upload fails
- Designed for Windows Task Scheduler integration
- Returns appropriate exit codes (0 = success, 1 = failure)

## Configuration

Before running the application, you need to update the configuration constants in `Program.cs`:

### FTP Configuration
```csharp
private const string FtpServerUrl = "ftp://yourserver.com/";
private const string FtpUsername = "ftpusername";
private const string FtpPassword = "ftppassword";
```

### Email Configuration
```csharp
private const string SmtpServer = "smtp.gmail.com";
private const int SmtpPort = 587;
private const string EmailUsername = "<EMAIL>";
private const string EmailPassword = "your-app-password";
private const string FromEmail = "<EMAIL>";
private const string FromName = "FTP Health Checker";
```

**Note:** For Gmail, you'll need to use an App Password instead of your regular password. Enable 2FA and generate an App Password in your Google Account settings.

### Email Recipients
The application is configured to send failure notifications to:
- **To:** <EMAIL>
- **CC:** <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

## Building the Application

```bash
dotnet build
```

## Running the Application

```bash
dotnet run
```

Or run the compiled executable:
```bash
.\bin\Debug\net8.0\FTPHealthChecker.exe
```

## Windows Task Scheduler Setup

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., Daily at specific time)
4. Set action to start the program: `path\to\FTPHealthChecker.exe`
5. Configure additional settings as needed

## Files

- `Program.cs` - Main application logic
- `dummy_check.xml` - Template XML file that gets copied for each health check
- `FTPHealthChecker.csproj` - Project configuration with dependencies

## Dependencies

- MailKit (4.3.0) - For SMTP email functionality

## Exit Codes

- **0** - Success (FTP upload completed successfully)
- **1** - Failure (FTP upload failed, email notification sent)

## Logging

The application outputs status messages to the console, which can be captured by Task Scheduler for logging purposes.

## Error Handling

- FTP connection and upload errors are caught and trigger email notifications
- Email sending errors are logged but don't prevent the application from exiting with the correct status
- Temporary files are cleaned up even if errors occur
- Orphaned temporary files from previous runs are cleaned up automatically
