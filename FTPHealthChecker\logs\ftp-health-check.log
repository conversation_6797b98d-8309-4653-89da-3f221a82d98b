﻿[2025-06-05 11:16:51] [INFO] Starting FTP Health Check...
[2025-06-05 11:17:03] [INFO] Created temporary file: healthcheck_20250605_111654.xml
[2025-06-05 11:17:10] [INFO] Uploading healthcheck_20250605_111654.xml to FTP server: ftp://192.168.1.14/FPNetXML/TestImport/Import/FTPhealthCheck/
[2025-06-05 11:17:13] [ERROR] FTP upload failed: The remote server returned an error: (550) File unavailable (e.g., file not found, no access).: The remote server returned an error: (550) File unavailable (e.g., file not found, no access).
StackTrace:    at System.Net.FtpWebRequest.EndGetRequestStream(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, <PERSON><PERSON><PERSON> requiresSynchronization)
--- End of stack trace from previous location ---
   at System.Net.WebRequest.GetRequestStreamAsync()
   at FTPHealthChecker.Program.UploadFileToFtp(String localFilePath) in E:\Shri\ScedularFTP\FTPHealthChecker\Program.cs:line 123
[2025-06-05 11:17:25] [ERROR] FTP Health Check failed: The remote server returned an error: (550) File unavailable (e.g., file not found, no access).: The remote server returned an error: (550) File unavailable (e.g., file not found, no access).
StackTrace:    at System.Net.FtpWebRequest.EndGetRequestStream(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at System.Net.WebRequest.GetRequestStreamAsync()
   at FTPHealthChecker.Program.UploadFileToFtp(String localFilePath) in E:\Shri\ScedularFTP\FTPHealthChecker\Program.cs:line 123
   at FTPHealthChecker.Program.Main(String[] args) in E:\Shri\ScedularFTP\FTPHealthChecker\Program.cs:line 40
[2025-06-05 11:17:26] [INFO] Cleaned up temporary file: .\healthcheck_20250605_103737.xml
[2025-06-05 11:17:26] [INFO] Cleaned up temporary file: .\healthcheck_20250605_111654.xml
[2025-06-05 11:17:43] [INFO] Sending failure notification email...
[2025-06-05 11:18:22] [INFO] Failure notification email sent successfully.
