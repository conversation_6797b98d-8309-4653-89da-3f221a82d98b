{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FTPHealthChecker/1.0.0": {"dependencies": {"MailKit": "4.3.0"}, "runtime": {"FTPHealthChecker.dll": {}}}, "BouncyCastle.Cryptography/2.2.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.1.47552"}}}, "MailKit/4.3.0": {"dependencies": {"MimeKit": "4.3.0"}, "runtime": {"lib/net6.0/MailKit.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "MimeKit/4.3.0": {"dependencies": {"BouncyCastle.Cryptography": "2.2.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.Pkcs": "7.0.3", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net6.0/MimeKit.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "System.Formats.Asn1/7.0.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Pkcs/7.0.3": {"dependencies": {"System.Formats.Asn1": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.823.31807"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.823.31807"}}}, "System.Text.Encoding.CodePages/7.0.0": {}}}, "libraries": {"FTPHealthChecker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-A6Zr52zVqJKt18ZBsTnX0qhG0kwIQftVAjLmszmkiR/trSp8H+xj1gUOzk7XHwaKgyREMSV1v9XaKrBUeIOdvQ==", "path": "bouncycastle.cryptography/2.2.1", "hashPath": "bouncycastle.cryptography.2.2.1.nupkg.sha512"}, "MailKit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVmB3Nr0JpqhyMiXOGWMin+QvRKpucGpSFBCav9dG6jEJPdBV+yp1RHVpKzxZPfT+0adaBuZlMFdbIciZo1EWA==", "path": "mailkit/4.3.0", "hashPath": "mailkit.4.3.0.nupkg.sha512"}, "MimeKit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-39KDXuERDy5VmHIn7NnCWvIVp/Ar4qnxZWg9m06DfRqDbW1B6zFv9o3Tdoa4CCu71tE/0SRqRCN5Z+bbffw6uw==", "path": "mimekit/4.3.0", "hashPath": "mimekit.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+nfpV0afLmvJW8+pLlHxRjz3oZJw4fkyU9MMEaMhCsHi/SN9bGF9q79ROubDiwTiCHezmK0uCWkPP7tGFP/4yg==", "path": "system.formats.asn1/7.0.0", "hashPath": "system.formats.asn1.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yhwEHH5Gzl/VoADrXtt5XC95OFoSjNSWLHNutE7GwdOgefZVRvEXRSooSpL8HHm3qmdd9epqzsWg28UJemt22w==", "path": "system.security.cryptography.pkcs/7.0.3", "hashPath": "system.security.cryptography.pkcs.7.0.3.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}}}