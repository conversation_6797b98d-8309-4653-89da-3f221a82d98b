using System.Net;
using System.Text;
using MailKit.Net.Smtp;
using MimeKit;
using Microsoft.Extensions.Configuration;

namespace FTPHealthChecker;

class Program
{
    private static AppConfiguration _config = new();
    private static FileLogger _logger = new(new LoggingSettings());

    static async Task<int> Main(string[] args)
    {
        try
        {
            // Load configuration
            LoadConfiguration();

            // Initialize logger with loaded settings
            _logger = new FileLogger(_config.LoggingSettings);

            _logger.LogInfo("Starting FTP Health Check...");

            // Create a copy of the dummy XML file for this health check
            string sourceFile = "dummy_check.xml";
            string tempFile = $"healthcheck_{DateTime.Now:yyyyMMdd_HHmmss}.xml";

            if (!File.Exists(sourceFile))
            {
                throw new FileNotFoundException($"Source file '{sourceFile}' not found. Make sure it exists in the application directory.");
            }

            // Copy the dummy file
            File.Copy(sourceFile, tempFile);
            _logger.LogInfo($"Created temporary file: {tempFile}");

            // Attempt FTP upload
            bool uploadSuccess = await UploadFileToFtp(tempFile);

            if (uploadSuccess)
            {
                // Upload successful - delete the temporary file and exit silently
                File.Delete(tempFile);
                _logger.LogInfo("FTP Health Check completed successfully.");
                return 0; // Success exit code
            }
            else
            {
                // This shouldn't happen as exceptions should be caught, but just in case
                File.Delete(tempFile);
                await SendFailureEmail("Unknown error occurred during FTP upload.");
                return 1; // Failure exit code
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"FTP Health Check failed: {ex.Message}", ex);

            // Clean up any temporary files
            CleanupTempFiles();

            // Send failure email
            await SendFailureEmail(ex.Message);
            return 1; // Failure exit code
        }
    }

    private static void LoadConfiguration()
    {
        try
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables("FTPHEALTHCHECK_"); // Allow environment variable overrides

            var configuration = builder.Build();

            // Bind configuration sections
            configuration.GetSection("FtpSettings").Bind(_config.FtpSettings);
            configuration.GetSection("EmailSettings").Bind(_config.EmailSettings);
            configuration.GetSection("LoggingSettings").Bind(_config.LoggingSettings);

            // Override with environment variables if they exist
            _config.FtpSettings.ServerUrl = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_FTP_SERVERURL") ?? _config.FtpSettings.ServerUrl;
            _config.FtpSettings.Username = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_FTP_USERNAME") ?? _config.FtpSettings.Username;
            _config.FtpSettings.Password = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_FTP_PASSWORD") ?? _config.FtpSettings.Password;

            _config.EmailSettings.SmtpServer = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_EMAIL_SMTPSERVER") ?? _config.EmailSettings.SmtpServer;
            _config.EmailSettings.Username = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_EMAIL_USERNAME") ?? _config.EmailSettings.Username;
            _config.EmailSettings.Password = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_EMAIL_PASSWORD") ?? _config.EmailSettings.Password;
            _config.EmailSettings.FromEmail = Environment.GetEnvironmentVariable("FTPHEALTHCHECK_EMAIL_FROMEMAIL") ?? _config.EmailSettings.FromEmail;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to load configuration: {ex.Message}");
            throw;
        }
    }

    private static async Task<bool> UploadFileToFtp(string localFilePath)
    {
        try
        {
            _logger.LogInfo($"Uploading {localFilePath} to FTP server: {_config.FtpSettings.ServerUrl}");

            // Create FTP request
            string ftpUrl = _config.FtpSettings.ServerUrl.TrimEnd('/') + "/" + Path.GetFileName(localFilePath);
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
            request.Method = WebRequestMethods.Ftp.UploadFile;
            request.Credentials = new NetworkCredential(_config.FtpSettings.Username, _config.FtpSettings.Password);
            request.UseBinary = true;
            request.UsePassive = true;
            request.KeepAlive = false;

            // Read file content
            byte[] fileContents = await File.ReadAllBytesAsync(localFilePath);
            request.ContentLength = fileContents.Length;

            // Upload file
            using (Stream requestStream = await request.GetRequestStreamAsync())
            {
                await requestStream.WriteAsync(fileContents, 0, fileContents.Length);
            }

            // Get response
            using (FtpWebResponse response = (FtpWebResponse)await request.GetResponseAsync())
            {
                _logger.LogInfo($"Upload completed. Status: {response.StatusDescription}");
                return response.StatusCode == FtpStatusCode.ClosingData ||
                       response.StatusCode == FtpStatusCode.FileActionOK;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"FTP upload failed: {ex.Message}", ex);
            throw; // Re-throw to be handled by main method
        }
    }

    private static async Task SendFailureEmail(string errorMessage)
    {
        try
        {
            _logger.LogInfo("Sending failure notification email...");

            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_config.EmailSettings.FromName, _config.EmailSettings.FromEmail));
            message.To.Add(new MailboxAddress("", _config.EmailSettings.ToEmail));

            // Add CC recipients
            foreach (string ccEmail in _config.EmailSettings.CcEmails)
            {
                message.Cc.Add(new MailboxAddress("", ccEmail));
            }

            message.Subject = "FTP Health Check Failed";

            var bodyBuilder = new BodyBuilder
            {
                TextBody = $@"FTP Health Check Failed

                The scheduled FTP health check has failed.

                Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

                Please check the FTP connectivity.

                This is an automated message from the FTP Health Checker"
            };

            message.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                await client.ConnectAsync(_config.EmailSettings.SmtpServer, _config.EmailSettings.SmtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_config.EmailSettings.Username, _config.EmailSettings.Password);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
            }

            _logger.LogInfo("Failure notification email sent successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to send email notification: {ex.Message}", ex);
            // Don't throw here as we don't want email failures to mask the original FTP failure
        }
    }

    private static void CleanupTempFiles()
    {
        try
        {
            // Clean up any temporary health check files that might be left behind
            string[] tempFiles = Directory.GetFiles(".", "healthcheck_*.xml");
            foreach (string file in tempFiles)
            {
                try
                {
                    File.Delete(file);
                    _logger.LogInfo($"Cleaned up temporary file: {file}");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Could not delete temporary file {file}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error during cleanup: {ex.Message}", ex);
        }
    }
}
