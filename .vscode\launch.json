{"version": "0.2.0", "configurations": [{"name": "Debug FTP Health Checker", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/FTPHealthChecker/bin/Debug/net8.0/FTPHealthChecker.dll", "args": [], "cwd": "${workspaceFolder}/FTPHealthChecker", "console": "integratedTerminal", "stopAtEntry": false, "env": {"DOTNET_ENVIRONMENT": "Development"}}, {"name": "Run FTP Health Checker (No Debug)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/FTPHealthChecker/bin/Debug/net8.0/FTPHealthChecker.dll", "args": [], "cwd": "${workspaceFolder}/FTPHealthChecker", "console": "integratedTerminal", "stopAtEntry": false, "justMyCode": false, "env": {"DOTNET_ENVIRONMENT": "Development"}}]}